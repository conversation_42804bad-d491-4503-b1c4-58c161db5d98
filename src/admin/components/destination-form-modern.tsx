import {
  Button,
  Input,
  Drawer,
  Text,
  Label,
  Switch,
  Heading,
  Tooltip,
} from "@camped-ai/ui";
import { useForm } from "react-hook-form";
import DestinationMediaSection from "./destination/destination-media-section";
import { MediaField } from "./hotel/media-item";
import { useState, useEffect } from "react";
import { Info, Globe, MapPin, Tag, Building } from "lucide-react";
import { DestinationFaqData } from "./destination-form";
import { TextareaField } from "./ai-enhanced-inputs";
import CustomSelect from "./custom-select";

export type DestinationFormData = {
  name: string;
  handle: string;
  description: string;
  is_active: boolean;
  is_featured: boolean;
  country: string;
  currency: string;
  location: string | null;
  tags: string[] | null;
  website?: string | null;
  category_id?: string;
  media?: MediaField[];
  image_ids?: string[];
  thumbnail_image_id?: string;
  faqs?: DestinationFaqData[];
  id?: string;
  customCountry?: string;
  customCurrency?: string;
};

type DestinationFormProps = {
  formData: DestinationFormData;
  setFormData: (data: DestinationFormData) => void;
  onSubmit: (updatedData?: DestinationFormData) => Promise<boolean>;
  isEdit?: boolean;
  closeModal: () => void;
};

const DestinationFormModern = ({
  formData,
  setFormData,
  onSubmit,
  isEdit,
  closeModal,
}: DestinationFormProps) => {
  const form = useForm<DestinationFormData>({
    defaultValues: formData,
  });

  // Reset form when formData changes (e.g., when creating a new destination after submitting one)
  useEffect(() => {
    // Reset the form with the new formData
    form.reset(formData);
  }, [formData, form]);

  const [activeTab, setActiveTab] = useState("basics");
  const [tagsInput, setTagsInput] = useState(() => {
    if (!formData.tags) return "";
    if (Array.isArray(formData.tags)) return formData.tags.join(", ");
    if (typeof formData.tags === "string") {
      try {
        // Try to parse if it's a JSON string
        const parsed = JSON.parse(formData.tags);
        return Array.isArray(parsed) ? parsed.join(", ") : "";
      } catch (e) {
        // If not a valid JSON, return as is (might be comma-separated already)
        return formData.tags;
      }
    }
    return "";
  });



  // Popular country options
  const countryOptions = [
    { label: "United States", value: "United States" },
    { label: "United Kingdom", value: "United Kingdom" },
    { label: "France", value: "France" },
    { label: "Italy", value: "Italy" },
    { label: "Spain", value: "Spain" },
    { label: "Germany", value: "Germany" },
    { label: "Japan", value: "Japan" },
    { label: "Australia", value: "Australia" },
    { label: "Canada", value: "Canada" },
    { label: "Switzerland", value: "Switzerland" },
    { label: "Thailand", value: "Thailand" },
    { label: "Greece", value: "Greece" },
    { label: "Mexico", value: "Mexico" },
    { label: "Brazil", value: "Brazil" },
    { label: "India", value: "India" },
    { label: "China", value: "China" },
  ];

  // Handle tags input change
  const handleTagsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setTagsInput(e.target.value);
    // Convert comma-separated string to array
    const tagsArray = e.target.value
      ? e.target.value
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag)
      : [];
    setFormData({ ...formData, tags: tagsArray.length > 0 ? tagsArray : null });
  };

  // Handle form submission
  const handleSubmit = async () => {
    // Get the form values including media
    const formValues = form.getValues();

    console.log('Form values from useForm:', formValues);
    console.log('Current formData state:', formData);

    // Ensure tags is properly formatted
    const tagsArray = tagsInput
      ? tagsInput
        .split(",")
        .map((tag) => tag.trim())
        .filter((tag) => tag)
      : [];
    const finalTags = tagsArray.length > 0 ? tagsArray : null;

    // Make sure we're getting the media field from the form
    const mediaFromForm = formValues.media || [];

    // Create the updated form data, prioritizing the current state values
    // for fields that are managed by our custom components
    const updatedFormData = {
      ...formValues,           // Base values from the form
      ...formData,            // Override with current state values
      country: formData.country,       // Ensure custom select values are used
      currency: formData.currency,
      category_id: formData.category_id,
      media: mediaFromForm,   // Explicitly include the media field
      tags: finalTags,
      is_featured: formValues.is_featured || false,
      faqs: formData.faqs || [], // Explicitly preserve FAQ data
    };

    console.log('FAQ data being submitted:', formData.faqs);

    console.log('Updated form data before submit:', updatedFormData);

    // Update the state
    setFormData(updatedFormData);

    // Pass the updated form data directly to onSubmit
    const success = await onSubmit(updatedFormData);
    if (success) {
      closeModal();
    }
  };

  return (
    <>
      <Drawer.Header className=" ">
        <div className="flex justify-between items-center w-full">
          <Heading level="h2" className="text-xl font-semibold">
            {isEdit ? "Edit Destination" : "Add New Destination"}
          </Heading>
        </div>
      </Drawer.Header>

      <Drawer.Body className="flex flex-col overflow-y-auto  flex-1">
        <div className="w-full">
          <div className="bg-white border-b border-gray-200 mb-6">
            <nav className="flex space-x-8">
              <button
                onClick={() => setActiveTab("basics")}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${activeTab === "basics"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
              >
                <Building size={18} />
                <span>Basic Information</span>
              </button>
              <button
                onClick={() => setActiveTab("details")}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${activeTab === "details"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
              >
                <Info size={18} />
                <span>Additional Details</span>
              </button>
              <button
                onClick={() => setActiveTab("media")}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${activeTab === "media"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <rect x="3" y="3" width="18" height="18" rx="2" ry="2" />
                  <circle cx="8.5" cy="8.5" r="1.5" />
                  <polyline points="21 15 16 10 5 21" />
                </svg>
                <span>Images</span>
              </button>
              <button
                onClick={() => setActiveTab("faqs")}
                className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${activeTab === "faqs"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                  }`}
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="18"
                  height="18"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <circle cx="12" cy="12" r="10" />
                  <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3" />
                  <path d="M12 17h.01" />
                </svg>
                <span>FAQs</span>
              </button>
            </nav>
          </div>
          <div className="">
            {/* Basic Information Tab */}
            {activeTab === "basics" && (
              <div className="space-y-6">
                <div className="bg-white pt-4 p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-lg font-medium mb-4">
                    Destination Information
                  </h3>

                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="name" className="block mb-1 font-medium">
                        Destination Name <span className="text-red-500">*</span>
                      </Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) =>
                          setFormData({
                            ...formData,
                            name: e.target.value,
                            handle: e.target.value
                              .toLowerCase()
                              .replace(/\s+/g, "-"),
                          })
                        }
                        placeholder="e.g. Swiss Alps"
                        className="w-full h-9 text-sm"
                      />
                      <Text className="text-xs text-gray-500 mt-1">
                        The name of the destination as it will appear to users
                      </Text>
                    </div>

                    <div>
                      <Label
                        htmlFor="handle"
                        className="mb-1 font-medium flex items-center gap-1"
                      >
                        URL Handle <span className="text-red-500">*</span>
                        <Tooltip content="This will be used in the URL for this destination (e.g. /destinations/swiss-alps)">
                          <Info size={12} className="text-gray-400" />
                        </Tooltip>
                      </Label>
                      <Input
                        id="handle"
                        value={formData.handle}
                        onChange={(e) =>
                          setFormData({ ...formData, handle: e.target.value })
                        }
                        placeholder="e.g. swiss-alps"
                        className="w-full h-9 text-sm"
                      />
                    </div>

                    <div>
                      <TextareaField
                        id="description"
                        label="Description"
                        value={formData.description}
                        onChange={(value) =>
                          setFormData({
                            ...formData,
                            description: value,
                          })
                        }
                        placeholder="Describe this destination..."
                        rows={5}
                        contentType="description"
                        context={{
                          name: formData.name,
                          type: "destination",
                          country: formData.country,
                          location: formData.location,
                        }}
                        helpText="Provide a detailed description of the destination to help users understand what makes it special"
                      />
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-lg font-medium mb-4">Location Details</h3>

                  <div className="space-y-4">
                    <div>
                      <Label
                        htmlFor="country"
                        className="block mb-1 font-medium"
                      >
                        Country <span className="text-red-500">*</span>
                      </Label>
                      <CustomSelect
                        id="country"
                        options={[
                          ...countryOptions,
                          { label: "Other (type below)", value: "other" }
                        ]}
                        value={formData.country}
                        onChange={(value) => {
                          console.log('Country changed to:', value);
                          setFormData({ ...formData, country: value });
                          // Also update the form values
                          form.setValue('country', value);
                        }}
                        placeholder="Select a country"
                        className="w-full"
                      />
                      {formData.country === "other" && (
                        <Input
                          className="mt-2 h-9 text-sm"
                          placeholder="Enter country name"
                          value={formData.customCountry || ""}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              customCountry: e.target.value,
                              country: e.target.value,
                            })
                          }
                        />
                      )}
                    </div>

                    <div>
                      <Label
                        htmlFor="location"
                        className="mb-1 font-medium flex items-center gap-1"
                      >
                        Specific Location
                        <Tooltip content="More specific location within the country (e.g. city, region)">
                          <Info size={12} className="text-gray-400" />
                        </Tooltip>
                      </Label>
                      <div className="relative">
                        <Input
                          id="location"
                          value={formData.location || ""}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              location: e.target.value,
                            })
                          }
                          placeholder="e.g. Zermatt, Valais"
                          className="w-full pl-9 h-9 text-sm"
                        />
                      </div>
                    </div>
                  </div>
                </div>

                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-lg font-medium mb-4">
                    Visibility Settings
                  </h3>

                  <div className="space-y-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="is-active" className="font-medium">
                          Active
                        </Label>
                        <Text className="text-sm text-gray-500">
                          When active, the destination will be visible to users
                        </Text>
                      </div>
                      <Switch
                        id="is-active"
                        checked={formData.is_active}
                        onCheckedChange={(checked) =>
                          setFormData({ ...formData, is_active: checked })
                        }
                      />
                    </div>

                    <div className="flex items-center justify-between">
                      <div>
                        <Label htmlFor="is-featured" className="font-medium">
                          Featured
                        </Label>
                        <Text className="text-sm text-gray-500">
                          Featured destinations will be highlighted and shown prominently to users
                        </Text>
                      </div>
                      <Switch
                        id="is-featured"
                        checked={formData.is_featured}
                        onCheckedChange={(checked) =>
                          setFormData({ ...formData, is_featured: checked })
                        }
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Additional Details Tab */}
            {activeTab === "details" && (
              <div className="space-y-6">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-lg font-medium mb-4">
                    Additional Information
                  </h3>

                  <div className="space-y-4">

                    <div>
                      <Label
                        htmlFor="website"
                        className="mb-1 font-medium flex items-center gap-1"
                      >
                        Official Website
                        <Tooltip content="Official tourism website for this destination">
                          <Info size={12} className="text-gray-400" />
                        </Tooltip>
                      </Label>
                      <div className="relative">
                        <Globe
                          size={14}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                        />
                        <Input
                          id="website"
                          value={formData.website || ""}
                          onChange={(e) =>
                            setFormData({
                              ...formData,
                              website: e.target.value,
                            })
                          }
                          placeholder="e.g. https://www.myswitzerland.com"
                          className="w-full pl-9 h-9 text-sm"
                        />
                      </div>
                    </div>

                    <div>
                      <Label
                        htmlFor="tags"
                        className="mb-1 font-medium flex items-center gap-1"
                      >
                        Tags
                        <Tooltip content="Add tags to categorize this destination (e.g. mountains, beach, family-friendly)">
                          <Info size={12} className="text-gray-400" />
                        </Tooltip>
                      </Label>
                      <div className="relative">
                        <Tag
                          size={14}
                          className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                        />
                        <Input
                          id="tags"
                          value={tagsInput}
                          onChange={handleTagsChange}
                          placeholder="e.g. mountains, skiing, winter, luxury"
                          className="w-full pl-9 h-9 text-sm"
                        />
                      </div>
                      <Text className="text-xs text-gray-500 mt-1">
                        Separate tags with commas
                      </Text>
                      {formData.tags &&
                        Array.isArray(formData.tags) &&
                        formData.tags.length > 0 && (
                          <div className="flex flex-wrap gap-1 mt-2">
                            {formData.tags.map((tag, index) => (
                              <span
                                key={index}
                                className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                              >
                                {tag}
                              </span>
                            ))}
                          </div>
                        )}
                    </div>

                    <div>
                      <Label
                        htmlFor="category"
                        className="mb-1 font-medium flex items-center gap-1"
                      >
                        Category
                        <Tooltip content="Select a category for this destination">
                          <Info size={12} className="text-gray-400" />
                        </Tooltip>
                      </Label>
                      <CustomSelect
                        id="category"
                        options={[
                          { label: "Beach", value: "beach" },
                          { label: "Mountain", value: "mountain" },
                          { label: "City", value: "city" },
                          { label: "Countryside", value: "countryside" },
                          { label: "Island", value: "island" },
                          { label: "Desert", value: "desert" },
                          { label: "Forest", value: "forest" }
                        ]}
                        value={formData.category_id || ""}
                        onChange={(value) => {
                          console.log('Category changed to:', value);
                          setFormData({ ...formData, category_id: value });
                          // Also update the form values
                          form.setValue('category_id', value);
                        }}
                        placeholder="Select a category"
                        className="w-full"
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Media Tab */}
            {activeTab === "media" && (
              <div className="space-y-6">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <h3 className="text-lg font-medium mb-4">
                    Destination Images
                  </h3>
                  <Text className="text-sm text-gray-500 mb-4">
                    Upload images that showcase this destination. The first
                    image or the one marked as thumbnail will be used as the
                    main image.
                  </Text>

                  <DestinationMediaSection form={form} />
                </div>
              </div>
            )}

            {/* FAQs Tab */}
            {activeTab === "faqs" && (
              <div className="space-y-6">
                <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                  <div className="flex justify-between items-center mb-4">
                    <h3 className="text-lg font-medium">
                      Frequently Asked Questions
                    </h3>
                    <Button
                      variant="secondary"
                      size="small"
                      onClick={() => {
                        const newFaq = { question: "", answer: "" };
                        setFormData({
                          ...formData,
                          faqs: [...(formData.faqs || []), newFaq]
                        });
                      }}
                    >
                      Add FAQ
                    </Button>
                  </div>
                  <Text className="text-sm text-gray-500 mb-4">
                    Add frequently asked questions to help users understand more about this destination.
                  </Text>

                  {formData.faqs && formData.faqs.length > 0 ? (
                    <div className="space-y-4">
                      {formData.faqs.map((faq, index) => (
                        <div key={index} className="border rounded-lg p-4 space-y-3 bg-gray-50">
                          <div className="flex justify-between items-start">
                            <Text className="text-sm font-medium text-gray-700">FAQ {index + 1}</Text>
                            <Button
                              variant="transparent"
                              size="small"
                              onClick={() => {
                                const updatedFaqs = formData.faqs?.filter((_, i) => i !== index) || [];
                                setFormData({ ...formData, faqs: updatedFaqs });
                              }}
                              className="text-red-500 hover:text-red-700"
                            >
                              Remove
                            </Button>
                          </div>
                          <div>
                            <Label className="text-xs text-gray-600 font-medium">Question</Label>
                            <Input
                              value={faq.question}
                              onChange={(e) => {
                                const updatedFaqs = [...(formData.faqs || [])];
                                updatedFaqs[index] = { ...faq, question: e.target.value };
                                setFormData({ ...formData, faqs: updatedFaqs });
                              }}
                              placeholder="Enter question"
                              className="mt-1 h-9 text-sm"
                            />
                          </div>
                          <div>
                            <Label className="text-xs text-gray-600 font-medium">Answer</Label>
                            <TextareaField
                              value={faq.answer}
                              onChange={(value) => {
                                const updatedFaqs = [...(formData.faqs || [])];
                                updatedFaqs[index] = { ...faq, answer: value };
                                setFormData({ ...formData, faqs: updatedFaqs });
                              }}
                              placeholder="Enter answer"
                              rows={3}
                              contentType="description"
                              context={{
                                question: faq.question,
                                destination: formData.name,
                                country: formData.country,
                                location: formData.location,
                              }}
                              className="mt-1"
                            />
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-8 text-gray-500 bg-gray-50 rounded-lg">
                      <svg
                        className="mx-auto h-12 w-12 text-gray-400 mb-4"
                        fill="none"
                        viewBox="0 0 24 24"
                        stroke="currentColor"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M8.228 9c.549-1.165 2.03-2 3.772-2 2.21 0 4 1.343 4 3 0 1.4-1.278 2.575-3.006 2.907-.542.104-.994.54-.994 1.093m0 3h.01M21 12a9 9 0 11-18 0 9 9 0 0118 0z"
                        />
                      </svg>
                      <Text>No FAQs added yet. Click "Add FAQ" to get started.</Text>
                    </div>
                  )}
                </div>
              </div>
            )}
          </div>
        </div>
      </Drawer.Body>

      <Drawer.Footer className="flex justify-end gap-2 p-4 border-t bg-white">
        <Button variant="secondary" onClick={closeModal}>
          Cancel
        </Button>
        <Button
          variant="primary"
          onClick={handleSubmit}
          disabled={!formData.name || !formData.handle}
        >
          {isEdit ? "Update Destination" : "Create Destination"}
        </Button>
      </Drawer.Footer>
    </>
  );
};

export default DestinationFormModern;
