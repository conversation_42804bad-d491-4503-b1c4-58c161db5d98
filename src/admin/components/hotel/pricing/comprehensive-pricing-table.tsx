import React, { useState, useEffect } from "react";
import {
  But<PERSON>,
  Container,
  Heading,
  Text,
  toast,
  Toaster,
  Input,
  Label,
  FocusModal,
} from "@camped-ai/ui";
import {
  DollarSign,
  Euro,
  PoundSterling,
  Save,
  Loader2,
  Calendar,
  Calculator,
} from "lucide-react";
import { useAdminHotelPricing } from "../../../hooks/hotel/use-admin-hotel-pricing";
import { useAdminCurrencies } from "../../../hooks/use-admin-currencies";
import { CurrencySelector } from "../../common/currency-selector";

import { format } from "date-fns";
import BulkPriceUpdateModal from "./bulk-price-update-modal";

// Helper function to generate a unique ID
const generateId = () => {
  return (
    Math.random().toString(36).substring(2, 15) +
    Math.random().toString(36).substring(2, 15)
  );
};

type RoomConfig = {
  id: string;
  title: string;
  handle?: string;
  description?: string;
};

type OccupancyConfig = {
  id: string;
  name: string;
  is_default?: boolean;
};

type MealPlan = {
  id: string;
  name: string;
  is_default?: boolean;
};

type SeasonalPeriod = {
  id: string;
  name: string;
  start_date: string;
  end_date: string;
};

type PricingRow = {
  id: string;
  roomConfigId: string;
  occupancyTypeId: string;
  mealPlanId: string | null; // null for extra beds and cots
  seasonalPeriodId?: string; // null/undefined for base pricing
  prices: {
    mon: number;
    tue: number;
    wed: number;
    thu: number;
    fri: number;
    sat: number;
    sun: number;
  };
  modified: boolean;
};

type ComprehensivePricingTableProps = {
  hotelId: string;
  roomConfigs: RoomConfig[];
  occupancyConfigs: OccupancyConfig[];
  mealPlans: MealPlan[];
  seasonalPeriods: SeasonalPeriod[];
  setSeasonalPeriods: React.Dispatch<React.SetStateAction<SeasonalPeriod[]>>;
  initialPrices?: Record<string, any>;
  onSave?: (data: any) => void;
};

const ComprehensivePricingTable: React.FC<ComprehensivePricingTableProps> = ({
  hotelId,
  roomConfigs,
  occupancyConfigs,
  mealPlans,
  seasonalPeriods,
  setSeasonalPeriods,
  initialPrices = {},
  onSave,
}) => {
  const [pricingRows, setPricingRows] = useState<PricingRow[]>([]);
  const [currencyCode, setCurrencyCode] = useState("");
  const [isLoading, setIsLoading] = useState(true);
  const [isSaving, setIsSaving] = useState(false);
  const [selectedRoomConfig, setSelectedRoomConfig] = useState<string>("");
  const [selectedOccupancyConfig, setSelectedOccupancyConfig] =
    useState<string>("");
  const [selectedMealPlan, setSelectedMealPlan] = useState<string>("");
  const [selectedSeasonFilter, setSelectedSeasonFilter] =
    useState<string>("all");
  const [showAllDays, setShowAllDays] = useState(true);

  const {
    currencies,
    defaultCurrency,
    isLoading: isLoadingCurrencies,
  } = useAdminCurrencies();

  // Get current currency object for proper formatting
  const currentCurrency =
    currencies.find((c) => c.currency_code === currencyCode) || defaultCurrency;

  // Helper function to format price display
  const formatPrice = (amount: number | null | undefined): string => {
    if (!amount) return "";

    // The amount is already in display units (dollars) from the data loading
    // Return as string without forcing decimal places
    return amount.toString();
  };

  // Helper function to get currency symbol
  const getCurrencySymbol = (): string => {
    return currentCurrency?.symbol || currencyCode;
  };

  // Helper function to parse user input (display format) to component state format
  const parsePrice = (displayValue: string): number => {
    // Parse the display value and return it as-is for component state
    // The component state stores values in display format (dollars)
    return parseFloat(displayValue) || 0;
  };

  // State for seasonal period modal
  const [isSeasonalModalOpen, setIsSeasonalModalOpen] = useState(false);
  const [newSeasonName, setNewSeasonName] = useState("");

  // State for bulk price update modal
  const [isBulkUpdateModalOpen, setIsBulkUpdateModalOpen] = useState(false);

  // Initialize dates with noon time to avoid timezone issues
  const today = new Date();
  today.setHours(12, 0, 0, 0);

  const nextWeek = new Date(today);
  nextWeek.setDate(today.getDate() + 7);

  const [newSeasonStartDate, setNewSeasonStartDate] = useState<Date>(today);
  const [newSeasonEndDate, setNewSeasonEndDate] = useState<Date>(nextWeek);
  const [isAddingSeasonalPeriod, setIsAddingSeasonalPeriod] = useState(false);

  const { savePricing } = useAdminHotelPricing();

  const weekdays = [
    { id: "mon", name: "Monday" },
    { id: "tue", name: "Tuesday" },
    { id: "wed", name: "Wednesday" },
    { id: "thu", name: "Thursday" },
    { id: "fri", name: "Friday" },
    { id: "sat", name: "Saturday" },
    { id: "sun", name: "Sunday" },
  ];

  // Initialize data when components load
  useEffect(() => {
    if (!isLoadingCurrencies) {
      initializeData();
    }
  }, [
    initialPrices,
    roomConfigs,
    occupancyConfigs,
    mealPlans,
    seasonalPeriods,
    isLoadingCurrencies,
  ]);

  // Set default currency when currencies are loaded
  useEffect(() => {
    if (defaultCurrency && !currencyCode) {
      setCurrencyCode(defaultCurrency.currency_code);
    }
  }, [defaultCurrency, currencyCode]);

  const initializeData = (targetCurrency?: string) => {
    const activeCurrency = targetCurrency || currencyCode;
    setIsLoading(true);

    const newRows: PricingRow[] = [];

    // For each room config, create pricing rows for all combinations
    roomConfigs.forEach((room) => {
      // Check if we have initial prices for this room
      const roomPrices = initialPrices[room.id] || {};

      // Filter pricing data by selected currency
      // Handle both single currency data and multi-currency data structures
      let currencyFilteredPrices: any = {};

      if (roomPrices[activeCurrency]) {
        // Multi-currency structure - look for currency-specific data
        console.log(
          `Room ${room.id} - Found ${activeCurrency} data:`,
          roomPrices[activeCurrency]
        );
        currencyFilteredPrices = roomPrices[activeCurrency];
      } else if (roomPrices.currency_code === activeCurrency) {
        // Legacy single currency structure - direct match
        console.log(
          `Room ${room.id} - Using legacy single currency data for ${activeCurrency}`
        );
        currencyFilteredPrices = roomPrices;
      } else {
        // No data for this currency - use empty object (will show 0 prices)
        console.log(
          `Room ${room.id} - No data for ${activeCurrency}, using empty`
        );
        currencyFilteredPrices = { weekday_rules: [], seasonal_prices: [] };
      }

      // Create base pricing rows (no seasonal period)
      occupancyConfigs.forEach((occupancy) => {
        // For extra beds and cots, only create one row with no meal plan
        const isExtraBed =
          (occupancy as any).type === "EXTRA_BED" ||
          occupancy.name?.toLowerCase().includes("extra bed");

        const isCot =
          (occupancy as any).type === "COT" ||
          occupancy.name?.toLowerCase().includes("cot");

        const isSpecialAccommodation = isExtraBed || isCot;

        const mealPlansToProcess = isSpecialAccommodation
          ? [{ id: null, name: "N/A" }] // Only one row for extra beds and cots
          : mealPlans; // All meal plans for other occupancy types

        mealPlansToProcess.forEach((mealPlan) => {
          // Check if we have a price for this combination
          const existingRule = currencyFilteredPrices.weekday_rules?.find(
            (rule: any) =>
              rule.occupancy_type_id === occupancy.id &&
              (isSpecialAccommodation
                ? !rule.meal_plan_id || rule.meal_plan_id === null
                : rule.meal_plan_id === mealPlan.id)
          );

          // Create pricing row with weekday prices
          newRows.push({
            id: existingRule?.id || generateId(),
            roomConfigId: room.id,
            occupancyTypeId: occupancy.id,
            mealPlanId: isSpecialAccommodation ? null : mealPlan.id,
            seasonalPeriodId: undefined, // Base pricing
            prices: {
              mon: existingRule
                ? (existingRule.weekday_prices?.mon || 0) / 100
                : 0,
              tue: existingRule
                ? (existingRule.weekday_prices?.tue || 0) / 100
                : 0,
              wed: existingRule
                ? (existingRule.weekday_prices?.wed || 0) / 100
                : 0,
              thu: existingRule
                ? (existingRule.weekday_prices?.thu || 0) / 100
                : 0,
              fri: existingRule
                ? (existingRule.weekday_prices?.fri || 0) / 100
                : 0,
              sat: existingRule
                ? (existingRule.weekday_prices?.sat || 0) / 100
                : 0,
              sun: existingRule
                ? (existingRule.weekday_prices?.sun || 0) / 100
                : 0,
            },
            modified: false,
          });

          // Create seasonal pricing rows
          seasonalPeriods.forEach((season) => {
            // Check if we have seasonal prices for this combination
            const seasonalPrices = currencyFilteredPrices.seasonal_prices || [];
            const existingSeasonalPrice = seasonalPrices.find(
              (sp: any) => sp.id === season.id || sp.name === season.name
            );

            const existingSeasonalRule =
              existingSeasonalPrice?.weekday_rules?.find(
                (rule: any) =>
                  rule.occupancy_type_id === occupancy.id &&
                  (isSpecialAccommodation
                    ? !rule.meal_plan_id || rule.meal_plan_id === null
                    : rule.meal_plan_id === mealPlan.id)
              );

            // Create pricing row with weekday prices for this season
            newRows.push({
              id: existingSeasonalRule?.id || generateId(),
              roomConfigId: room.id,
              occupancyTypeId: occupancy.id,
              mealPlanId: isSpecialAccommodation ? null : mealPlan.id,
              seasonalPeriodId: season.id,
              prices: {
                mon: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.mon || 0) / 100
                  : 0,
                tue: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.tue || 0) / 100
                  : 0,
                wed: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.wed || 0) / 100
                  : 0,
                thu: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.thu || 0) / 100
                  : 0,
                fri: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.fri || 0) / 100
                  : 0,
                sat: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.sat || 0) / 100
                  : 0,
                sun: existingSeasonalRule
                  ? (existingSeasonalRule.weekday_prices?.sun || 0) / 100
                  : 0,
              },
              modified: false,
            });
          });
        });
      });
    });

    setPricingRows(newRows);
    setIsLoading(false);
  };

  const handlePriceChange = (
    roomConfigId: string,
    occupancyTypeId: string,
    mealPlanId: string | null,
    seasonalPeriodId: string | undefined,
    day: string,
    value: number
  ) => {
    setPricingRows((prev) =>
      prev.map((row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        row.seasonalPeriodId === seasonalPeriodId
          ? {
              ...row,
              prices: { ...row.prices, [day]: value },
              modified: true,
            }
          : row
      )
    );
  };

  const handleCurrencyChange = (newCurrencyCode: string) => {
    console.log(
      `Currency change requested: ${currencyCode} → ${newCurrencyCode}`
    );
    setCurrencyCode(newCurrencyCode);
    // Re-initialize data with the new currency to filter pricing data
    // Pass the new currency directly to avoid state timing issues
    initializeData(newCurrencyCode);
  };

  const handleCopyBaseToSeasonal = (
    roomConfigId: string,
    occupancyTypeId: string,
    mealPlanId: string | null
  ) => {
    // Find the base pricing row
    const basePricingRow = pricingRows.find(
      (row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        !row.seasonalPeriodId
    );

    if (!basePricingRow) return;

    // Update all seasonal pricing rows for this combination
    setPricingRows((prev) =>
      prev.map((row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        row.seasonalPeriodId // Only seasonal rows
          ? {
              ...row,
              prices: { ...basePricingRow.prices },
              modified: true,
            }
          : row
      )
    );

    toast.success("Success", {
      description: "Base prices copied to all seasonal periods",
    });
  };

  const handleCopyToAllDays = (
    roomConfigId: string,
    occupancyTypeId: string,
    mealPlanId: string | null,
    seasonalPeriodId: string | undefined,
    sourceDay: string
  ) => {
    // Find the pricing row
    const pricingRow = pricingRows.find(
      (row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        row.seasonalPeriodId === seasonalPeriodId
    );

    if (!pricingRow) return;

    // Get the price from the source day
    const sourcePrice =
      pricingRow.prices[sourceDay as keyof typeof pricingRow.prices];

    // Create new prices with the same value for all days
    const newPrices = {
      mon: sourcePrice,
      tue: sourcePrice,
      wed: sourcePrice,
      thu: sourcePrice,
      fri: sourcePrice,
      sat: sourcePrice,
      sun: sourcePrice,
    };

    // Update the pricing row
    setPricingRows((prev) =>
      prev.map((row) =>
        row.roomConfigId === roomConfigId &&
        row.occupancyTypeId === occupancyTypeId &&
        row.mealPlanId === mealPlanId &&
        row.seasonalPeriodId === seasonalPeriodId
          ? {
              ...row,
              prices: newPrices,
              modified: true,
            }
          : row
      )
    );

    toast.success("Success", {
      description: `${sourceDay.toUpperCase()} price copied to all days`,
    });
  };

  const handleBulkPriceUpdate = (updatedRows: PricingRow[]) => {
    setPricingRows(updatedRows);
    toast.success("Success", {
      description: "Bulk price update applied successfully",
    });
  };

  const handleAddSeasonalPeriod = () => {
    // Validate inputs
    if (!newSeasonName.trim()) {
      toast.error("Error", {
        description: "Please enter a season name",
      });
      return;
    }

    // Make sure we have valid dates
    let startDate = newSeasonStartDate;
    let endDate = newSeasonEndDate;

    if (!startDate) {
      startDate = new Date();
    }

    if (!endDate) {
      endDate = new Date();
      // Set to 7 days after start date by default
      endDate.setDate(startDate.getDate() + 7);
    }

    if (startDate > endDate) {
      toast.error("Error", {
        description: "End date must be after start date",
      });
      return;
    }

    setIsAddingSeasonalPeriod(true);

    try {
      // Format dates safely
      const formatDate = (date: Date) => {
        try {
          return format(date, "yyyy-MM-dd");
        } catch (e) {
          console.error("Date formatting error:", e);
          // Fallback to ISO string and extract the date part
          return date.toISOString().split("T")[0];
        }
      };

      // Create a new seasonal period
      const newSeasonalPeriod: SeasonalPeriod = {
        id: generateId(),
        name: newSeasonName,
        start_date: formatDate(startDate),
        end_date: formatDate(endDate),
      };

      // Add the new seasonal period to the list
      setSeasonalPeriods((prevSeasons) => [...prevSeasons, newSeasonalPeriod]);

      // Create pricing rows for the new seasonal period
      const newRows: PricingRow[] = [];

      // For each room config, create pricing rows for all occupancy and meal plan combinations
      roomConfigs.forEach((room) => {
        occupancyConfigs.forEach((occupancy) => {
          // For extra beds and cots, only create one row with no meal plan
          const isExtraBed =
            (occupancy as any).type === "EXTRA_BED" ||
            occupancy.name?.toLowerCase().includes("extra bed");

          const isCot =
            (occupancy as any).type === "COT" ||
            occupancy.name?.toLowerCase().includes("cot");

          const isSpecialAccommodation = isExtraBed || isCot;

          const mealPlansToProcess = isSpecialAccommodation
            ? [{ id: null, name: "N/A" }] // Only one row for extra beds and cots
            : mealPlans; // All meal plans for other occupancy types

          mealPlansToProcess.forEach((mealPlan) => {
            // Find the base pricing row for this combination
            const basePricingRow = pricingRows.find(
              (row) =>
                row.roomConfigId === room.id &&
                row.occupancyTypeId === occupancy.id &&
                (isSpecialAccommodation
                  ? row.mealPlanId === null
                  : row.mealPlanId === mealPlan.id) &&
                !row.seasonalPeriodId
            );

            // Default prices if no base pricing row is found
            const defaultPrices = {
              mon: 0,
              tue: 0,
              wed: 0,
              thu: 0,
              fri: 0,
              sat: 0,
              sun: 0,
            };

            // Create a new pricing row for this combination with the new seasonal period
            newRows.push({
              id: generateId(),
              roomConfigId: room.id,
              occupancyTypeId: occupancy.id,
              mealPlanId: isSpecialAccommodation ? null : mealPlan.id,
              seasonalPeriodId: newSeasonalPeriod.id,
              prices: basePricingRow
                ? { ...basePricingRow.prices }
                : defaultPrices,
              modified: true, // Mark as modified so it gets saved
            });
          });
        });
      });

      // Update pricing rows state
      setPricingRows((prev) => [...prev, ...newRows]);

      // Close the modal and reset form
      setIsSeasonalModalOpen(false);
      setNewSeasonName("");
      setNewSeasonStartDate(new Date());
      setNewSeasonEndDate(new Date());

      toast.success("Success", {
        description: "Seasonal period added successfully",
      });
    } catch (error) {
      console.error("Error adding seasonal period:", error);
      toast.error("Error", {
        description:
          "Failed to add seasonal period: " +
          (error instanceof Error ? error.message : String(error)),
      });
    } finally {
      setIsAddingSeasonalPeriod(false);
    }
  };

  const handleSaveAll = async () => {
    setIsSaving(true);

    try {
      // Group pricing rows by room config and seasonal period
      const roomConfigPrices: Record<string, any> = {};

      // Only initialize room config prices for rooms that have modified data
      // Don't create empty pricing records for all rooms

      // Process base pricing rows
      const basePricingRows = pricingRows.filter(
        (row) => !row.seasonalPeriodId
      );
      basePricingRows.forEach((row) => {
        // Only include rows that have been modified
        if (row.modified) {
          console.log(
            `Saving modified base pricing for room ${row.roomConfigId}, currency ${currencyCode}`
          );

          // Initialize room config pricing if not exists
          if (!roomConfigPrices[row.roomConfigId]) {
            roomConfigPrices[row.roomConfigId] = {
              currency_code: currencyCode,
              weekday_rules: [],
              seasonal_prices: [],
            };
          }

          // Create weekday prices (convert from display format to smallest units)
          const weekdayPrices = {
            mon: Math.round(
              row.prices.mon *
                Math.pow(10, currentCurrency?.decimal_digits || 2)
            ),
            tue: Math.round(
              row.prices.tue *
                Math.pow(10, currentCurrency?.decimal_digits || 2)
            ),
            wed: Math.round(
              row.prices.wed *
                Math.pow(10, currentCurrency?.decimal_digits || 2)
            ),
            thu: Math.round(
              row.prices.thu *
                Math.pow(10, currentCurrency?.decimal_digits || 2)
            ),
            fri: Math.round(
              row.prices.fri *
                Math.pow(10, currentCurrency?.decimal_digits || 2)
            ),
            sat: Math.round(
              row.prices.sat *
                Math.pow(10, currentCurrency?.decimal_digits || 2)
            ),
            sun: Math.round(
              row.prices.sun *
                Math.pow(10, currentCurrency?.decimal_digits || 2)
            ),
          };

          roomConfigPrices[row.roomConfigId].weekday_rules.push({
            occupancy_type_id: row.occupancyTypeId,
            meal_plan_id: row.mealPlanId, // This will be null for extra beds
            weekday_prices: weekdayPrices,
          });
        }
      });

      // Process seasonal pricing rows
      const seasonalPricingRows = pricingRows.filter(
        (row) => row.seasonalPeriodId
      );

      // Group seasonal pricing rows by seasonal period
      const seasonalPeriodGroups: Record<string, any> = {};

      seasonalPricingRows.forEach((row) => {
        // Only include rows that have been modified
        if (row.modified) {
          console.log(
            `Saving modified seasonal pricing for room ${row.roomConfigId}, currency ${currencyCode}`
          );
          // Find the seasonal period
          const seasonalPeriod = seasonalPeriods.find(
            (sp) => sp.id === row.seasonalPeriodId
          );
          if (!seasonalPeriod) return;

          // Initialize room config pricing if not exists
          if (!roomConfigPrices[row.roomConfigId]) {
            roomConfigPrices[row.roomConfigId] = {
              currency_code: currencyCode,
              weekday_rules: [],
              seasonal_prices: [],
            };
          }

          // Create a key for this seasonal period and room config
          const key = `${row.roomConfigId}_${seasonalPeriod.id}`;

          // Initialize the group if it doesn't exist
          if (!seasonalPeriodGroups[key]) {
            seasonalPeriodGroups[key] = {
              roomConfigId: row.roomConfigId,
              seasonalPeriod,
              rules: [],
            };
          }

          // Create weekday prices (convert from display format to smallest units)
          const weekdayPrices = {
            mon: Math.round(
              row.prices.mon *
                Math.pow(10, currentCurrency?.decimal_digits || 2)
            ),
            tue: Math.round(
              row.prices.tue *
                Math.pow(10, currentCurrency?.decimal_digits || 2)
            ),
            wed: Math.round(
              row.prices.wed *
                Math.pow(10, currentCurrency?.decimal_digits || 2)
            ),
            thu: Math.round(
              row.prices.thu *
                Math.pow(10, currentCurrency?.decimal_digits || 2)
            ),
            fri: Math.round(
              row.prices.fri *
                Math.pow(10, currentCurrency?.decimal_digits || 2)
            ),
            sat: Math.round(
              row.prices.sat *
                Math.pow(10, currentCurrency?.decimal_digits || 2)
            ),
            sun: Math.round(
              row.prices.sun *
                Math.pow(10, currentCurrency?.decimal_digits || 2)
            ),
          };

          // Add this rule to the group
          seasonalPeriodGroups[key].rules.push({
            occupancy_type_id: row.occupancyTypeId,
            meal_plan_id: row.mealPlanId, // This will be null for extra beds
            weekday_prices: weekdayPrices,
          });
        }
      });

      // Now create seasonal prices for each room config
      Object.values(seasonalPeriodGroups).forEach((group: any) => {
        const { roomConfigId, seasonalPeriod, rules } = group;

        // Make sure the room config has a seasonal_prices array
        if (!roomConfigPrices[roomConfigId].seasonal_prices) {
          roomConfigPrices[roomConfigId].seasonal_prices = [];
        }

        // Add this seasonal period to the room config's seasonal prices
        roomConfigPrices[roomConfigId].seasonal_prices.push({
          id: seasonalPeriod.id,
          name: seasonalPeriod.name,
          start_date: seasonalPeriod.start_date,
          end_date: seasonalPeriod.end_date,
          weekday_rules: rules,
        });
      });

      // Save each room config's prices
      const savePromises = Object.entries(roomConfigPrices).map(
        async ([roomConfigId, data]) => {
          // Only save if there are weekday rules or seasonal prices
          if (
            data.weekday_rules.length > 0 ||
            data.seasonal_prices.length > 0
          ) {
            return savePricing(roomConfigId, data);
          }
          return null;
        }
      );

      const results = await Promise.all(savePromises);

      // Reset modified flags
      setPricingRows((prev) =>
        prev.map((row) => ({ ...row, modified: false }))
      );

      // Call onSave callback if provided
      if (onSave) {
        onSave({
          results,
          seasonal_periods: seasonalPeriods,
        });
      }

      toast.success("Success", {
        description: "Pricing saved successfully for all room configurations",
      });
    } catch (error) {
      console.error("Error saving pricing:", error);
      toast.error("Error", {
        description: "Failed to save pricing",
      });
    } finally {
      setIsSaving(false);
    }
  };

  // Filter pricing rows based on selected filters
  const filteredPricingRows = pricingRows.filter((row) => {
    // Filter by room config if selected
    if (selectedRoomConfig && row.roomConfigId !== selectedRoomConfig) {
      return false;
    }

    // Filter by occupancy config if selected
    if (
      selectedOccupancyConfig &&
      row.occupancyTypeId !== selectedOccupancyConfig
    ) {
      return false;
    }

    // Filter by meal plan if selected
    if (selectedMealPlan && row.mealPlanId !== selectedMealPlan) {
      return false;
    }

    // Filter by season based on selected season filter
    if (selectedSeasonFilter === "base" && row.seasonalPeriodId) {
      return false; // Hide seasonal rows when showing base only
    }
    if (selectedSeasonFilter !== "all" && selectedSeasonFilter !== "base") {
      // Show only specific season
      if (row.seasonalPeriodId !== selectedSeasonFilter) {
        return false;
      }
    }

    return true;
  });

  if (isLoading) {
    return (
      <Container>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded-md mb-4 w-1/3"></div>
          <div className="mb-6 h-4 bg-gray-200 rounded-md w-1/2"></div>

          <div className="flex justify-between mb-6">
            <div className="h-10 bg-gray-200 rounded-md w-32"></div>
            <div className="h-10 bg-gray-200 rounded-md w-28"></div>
          </div>

          <div className="rounded-lg border border-gray-200 shadow-sm overflow-hidden">
            <div className="h-12 bg-gray-100 rounded-t-md w-full"></div>
            <div className="h-16 bg-white border-t border-gray-200 w-full"></div>
            <div className="h-16 bg-gray-50 border-t border-gray-200 w-full"></div>
            <div className="h-16 bg-white border-t border-gray-200 w-full"></div>
          </div>
        </div>
      </Container>
    );
  }

  return (
    <Container>
      <Toaster />
      <div className="mb-6">
        <Heading level="h2">Comprehensive Pricing</Heading>
        <Text className="text-gray-600">
          View and edit all pricing in one place - base rates and seasonal rates
          for all room types
        </Text>
      </div>

      <div className="flex justify-between mb-6">
        <div className="flex gap-4">
          <CurrencySelector
            value={currencyCode}
            onChange={handleCurrencyChange}
            label="Currency"
            id="currency"
          />

          <div>
            <Label htmlFor="showAllSeasons" className="mb-1 block">
              Seasons
            </Label>
            <div className="relative inline-block">
              <select
                id="showAllSeasons"
                className="pl-4 pr-8 py-2 border border-gray-300 rounded-md bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
                value={selectedSeasonFilter}
                onChange={(e) => setSelectedSeasonFilter(e.target.value)}
              >
                <option value="all">All Seasons</option>
                <option value="base">Base Pricing Only</option>
                {seasonalPeriods.map((season) => (
                  <option key={season.id} value={season.id}>
                    {season.name}
                  </option>
                ))}
              </select>
              <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                <svg
                  className="w-4 h-4 text-gray-500"
                  fill="none"
                  stroke="currentColor"
                  viewBox="0 0 24 24"
                  xmlns="http://www.w3.org/2000/svg"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth="2"
                    d="M19 9l-7 7-7-7"
                  ></path>
                </svg>
              </div>
            </div>
          </div>
        </div>

        <div className="flex gap-2 self-end">
          <Button
            variant="secondary"
            onClick={() => setIsBulkUpdateModalOpen(true)}
            className="flex items-center gap-2 h-10"
            title="Update multiple prices at once by amount or percentage. Filter by room types, occupancy types, meal plans, and seasonal periods."
          >
            <Calculator className="w-4 h-4" />
            Bulk Update
          </Button>

          <Button
            variant="secondary"
            onClick={() => setIsSeasonalModalOpen(true)}
            className="flex items-center gap-2 h-10"
          >
            <Calendar className="w-4 h-4" />
            Add Season
          </Button>

          <Button
            variant="primary"
            onClick={handleSaveAll}
            className="flex items-center gap-2 h-10"
            disabled={isSaving}
          >
            {isSaving ? (
              <>
                <Loader2 className="w-4 h-4 animate-spin" />
                Saving...
              </>
            ) : (
              <>
                <Save className="w-4 h-4" />
                Save All Prices
              </>
            )}
          </Button>
        </div>
      </div>

      {/* Seasonal Period Modal */}
      <FocusModal
        open={isSeasonalModalOpen}
        onOpenChange={(open) => {
          setIsSeasonalModalOpen(open);
          if (!open) {
            // Reset form when closing
            setNewSeasonName("");

            const resetToday = new Date();
            resetToday.setHours(12, 0, 0, 0);

            const resetNextWeek = new Date(resetToday);
            resetNextWeek.setDate(resetToday.getDate() + 7);

            setNewSeasonStartDate(resetToday);
            setNewSeasonEndDate(resetNextWeek);
          }
        }}
      >
        <FocusModal.Content>
          <FocusModal.Header>
            <Button
              variant="secondary"
              onClick={() => setIsSeasonalModalOpen(false)}
              disabled={isAddingSeasonalPeriod}
            >
              Cancel
            </Button>
            <Text className="inter-large-semibold">Add Seasonal Period</Text>
            <Button
              variant="primary"
              onClick={handleAddSeasonalPeriod}
              disabled={isAddingSeasonalPeriod}
            >
              {isAddingSeasonalPeriod ? (
                <>
                  <Loader2 className="w-4 h-4 animate-spin mr-2" />
                  Adding...
                </>
              ) : (
                "Add Season"
              )}
            </Button>
          </FocusModal.Header>
          <FocusModal.Body>
            <div className="p-6 space-y-6">
              <div>
                <Label htmlFor="seasonName" className="mb-1 block">
                  Season Name
                </Label>
                <Input
                  id="seasonName"
                  value={newSeasonName}
                  onChange={(e) => setNewSeasonName(e.target.value)}
                  placeholder="e.g., Summer 2023"
                  className="w-full"
                  required
                />
              </div>

              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="startDate" className="mb-1 block">
                    Start Date
                  </Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <Calendar className="w-4 h-4 text-gray-500" />
                    </div>
                    <Input
                      id="startDate"
                      type="date"
                      value={
                        newSeasonStartDate
                          ? format(newSeasonStartDate, "yyyy-MM-dd")
                          : ""
                      }
                      onChange={(e) => {
                        if (e.target.value) {
                          // Create date at noon to avoid timezone issues
                          const date = new Date(e.target.value + "T12:00:00");
                          setNewSeasonStartDate(date);
                        } else {
                          setNewSeasonStartDate(new Date());
                        }
                      }}
                      className="pl-10 w-full"
                      required
                    />
                  </div>
                </div>

                <div>
                  <Label htmlFor="endDate" className="mb-1 block">
                    End Date
                  </Label>
                  <div className="relative">
                    <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
                      <Calendar className="w-4 h-4 text-gray-500" />
                    </div>
                    <Input
                      id="endDate"
                      type="date"
                      value={
                        newSeasonEndDate
                          ? format(newSeasonEndDate, "yyyy-MM-dd")
                          : ""
                      }
                      onChange={(e) => {
                        if (e.target.value) {
                          // Create date at noon to avoid timezone issues
                          const date = new Date(e.target.value + "T12:00:00");
                          setNewSeasonEndDate(date);
                        } else {
                          // Default to 7 days after start date
                          const date = new Date(newSeasonStartDate);
                          date.setDate(date.getDate() + 7);
                          setNewSeasonEndDate(date);
                        }
                      }}
                      className="pl-10 w-full"
                      required
                    />
                  </div>
                </div>
              </div>

              <div className="bg-blue-50 p-4 rounded-md border border-blue-100">
                <Text className="text-blue-800">
                  <strong>Note:</strong> Adding a new seasonal period will
                  create pricing rows for all room types, occupancy types, and
                  meal plans. Base prices will be copied to the new seasonal
                  period by default.
                </Text>
              </div>
            </div>
          </FocusModal.Body>
        </FocusModal.Content>
      </FocusModal>

      {/* Bulk Price Update Modal */}
      <BulkPriceUpdateModal
        isOpen={isBulkUpdateModalOpen}
        onClose={() => setIsBulkUpdateModalOpen(false)}
        roomConfigs={roomConfigs}
        occupancyConfigs={occupancyConfigs}
        mealPlans={mealPlans}
        seasonalPeriods={seasonalPeriods}
        currencyCode={currencyCode}
        pricingRows={pricingRows}
        onApplyUpdate={handleBulkPriceUpdate}
        onCurrencyChange={handleCurrencyChange}
      />

      {/* Filters for room, occupancy, and meal plan */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div>
          <Label htmlFor="roomConfig" className="mb-1 block">
            Room Type
          </Label>
          <div className="relative inline-block w-full">
            <select
              id="roomConfig"
              className="w-full pl-4 pr-8 py-2 border border-gray-300 rounded-md bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
              value={selectedRoomConfig}
              onChange={(e) => setSelectedRoomConfig(e.target.value)}
            >
              <option value="">All Room Types</option>
              {roomConfigs.map((room) => (
                <option key={room.id} value={room.id}>
                  {room.title}
                </option>
              ))}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <svg
                className="w-4 h-4 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                ></path>
              </svg>
            </div>
          </div>
        </div>

        <div>
          <Label htmlFor="occupancyConfig" className="mb-1 block">
            Occupancy Type
          </Label>
          <div className="relative inline-block w-full">
            <select
              id="occupancyConfig"
              className="w-full pl-4 pr-8 py-2 border border-gray-300 rounded-md bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
              value={selectedOccupancyConfig}
              onChange={(e) => setSelectedOccupancyConfig(e.target.value)}
            >
              <option value="">All Occupancy Types</option>
              {occupancyConfigs.map((occupancy) => (
                <option key={occupancy.id} value={occupancy.id}>
                  {occupancy.name}
                </option>
              ))}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <svg
                className="w-4 h-4 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                ></path>
              </svg>
            </div>
          </div>
        </div>

        <div>
          <Label htmlFor="mealPlan" className="mb-1 block">
            Meal Plan
          </Label>
          <div className="relative inline-block w-full">
            <select
              id="mealPlan"
              className="w-full pl-4 pr-8 py-2 border border-gray-300 rounded-md bg-white shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 appearance-none"
              value={selectedMealPlan}
              onChange={(e) => setSelectedMealPlan(e.target.value)}
            >
              <option value="">All Meal Plans</option>
              {mealPlans.map((mealPlan) => (
                <option key={mealPlan.id} value={mealPlan.id}>
                  {mealPlan.name}
                </option>
              ))}
            </select>
            <div className="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
              <svg
                className="w-4 h-4 text-gray-500"
                fill="none"
                stroke="currentColor"
                viewBox="0 0 24 24"
                xmlns="http://www.w3.org/2000/svg"
              >
                <path
                  strokeLinecap="round"
                  strokeLinejoin="round"
                  strokeWidth="2"
                  d="M19 9l-7 7-7-7"
                ></path>
              </svg>
            </div>
          </div>
        </div>
      </div>

      <div className="overflow-x-auto rounded-lg border border-gray-200 shadow-sm">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Room Type
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Occupancy
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Meal Plan
              </th>
              <th className="px-4 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Season
              </th>
              {showAllDays ? (
                <>
                  {weekdays.map((day) => (
                    <th
                      key={day.id}
                      className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider"
                    >
                      <div className="flex flex-col items-center">
                        <span>{day.name.slice(0, 3)}</span>
                        <span className="text-xs text-gray-400 font-normal">
                          ({getCurrencySymbol()})
                        </span>
                      </div>
                    </th>
                  ))}
                </>
              ) : (
                <th className="px-4 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                  <div className="flex flex-col items-center">
                    <span>Price</span>
                    <span className="text-xs text-gray-400 font-normal">
                      ({getCurrencySymbol()})
                    </span>
                  </div>
                </th>
              )}
              <th className="px-3 py-3 text-center text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {filteredPricingRows.map((row, index) => {
              // Find the room, occupancy, and meal plan objects
              const room = roomConfigs.find((r) => r.id === row.roomConfigId);
              const occupancy = occupancyConfigs.find(
                (o) => o.id === row.occupancyTypeId
              );
              const mealPlan = mealPlans.find((m) => m.id === row.mealPlanId);
              const seasonalPeriod = row.seasonalPeriodId
                ? seasonalPeriods.find((s) => s.id === row.seasonalPeriodId)
                : undefined;

              return (
                <tr
                  key={row.id}
                  className={index % 2 === 0 ? "bg-white" : "bg-gray-50"}
                >
                  <td className="px-4 py-4 whitespace-nowrap">
                    {room?.title || "Unknown Room"}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    {occupancy?.name || "Unknown Occupancy"}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    {(occupancy as any)?.type === "EXTRA_BED" ||
                    occupancy?.name?.toLowerCase().includes("extra bed") ||
                    (occupancy as any)?.type === "COT" ||
                    occupancy?.name?.toLowerCase().includes("cot") ? (
                      <span className="text-gray-500">-</span>
                    ) : (
                      mealPlan?.name || "Unknown Meal Plan"
                    )}
                  </td>
                  <td className="px-4 py-4 whitespace-nowrap">
                    {seasonalPeriod ? (
                      <div className="flex items-start">
                        <Calendar className="w-4 h-4 mr-2 text-blue-500 mt-1" />
                        <div className="flex flex-col">
                          <span>{seasonalPeriod.name}</span>
                          <span className="text-xs text-gray-500">
                            {format(
                              new Date(seasonalPeriod.start_date),
                              "MMM d"
                            )}{" "}
                            -{" "}
                            {format(
                              new Date(seasonalPeriod.end_date),
                              "MMM d, yyyy"
                            )}
                          </span>
                        </div>
                      </div>
                    ) : (
                      <span className="font-medium">Base Price</span>
                    )}
                  </td>

                  {showAllDays ? (
                    <>
                      {weekdays.map((day) => (
                        <td
                          key={day.id}
                          className="px-3 py-4 whitespace-nowrap"
                        >
                          <div className="flex justify-center">
                            <div className="relative">
                              <div className="absolute inset-y-0 left-0 flex items-center pl-2 pointer-events-none">
                                <span className="text-gray-500">
                                  {getCurrencySymbol()}
                                </span>
                              </div>
                              <Input
                                type="number"
                                className={`w-24 pl-6 py-1 border rounded-md focus:border-blue-500 focus:ring-2 focus:ring-blue-500 transition-all text-center ${
                                  row.modified
                                    ? "border-blue-500 bg-blue-50"
                                    : "border-gray-300"
                                }`}
                                value={formatPrice(
                                  row.prices[day.id as keyof typeof row.prices]
                                )}
                                onChange={(e) =>
                                  handlePriceChange(
                                    row.roomConfigId,
                                    row.occupancyTypeId,
                                    row.mealPlanId,
                                    row.seasonalPeriodId,
                                    day.id,
                                    parsePrice(e.target.value)
                                  )
                                }
                                min="0"
                                step="0.01"
                              />
                            </div>
                          </div>
                        </td>
                      ))}
                    </>
                  ) : (
                    <td className="px-4 py-4 whitespace-nowrap">
                      <div className="flex justify-center">
                        <div className="relative">
                          <div className="absolute inset-y-0 left-0 flex items-center pl-2 pointer-events-none">
                            <span className="text-gray-500">
                              {getCurrencySymbol()}
                            </span>
                          </div>
                          <Input
                            type="number"
                            className={`w-28 pl-6 py-1 border rounded-md focus:border-blue-500 focus:ring-2 focus:ring-blue-500 transition-all text-center ${
                              row.modified
                                ? "border-blue-500 bg-blue-50"
                                : "border-gray-300"
                            }`}
                            value={formatPrice(row.prices.mon)}
                            onChange={(e) => {
                              const value = parsePrice(e.target.value);
                              // First update Monday price
                              handlePriceChange(
                                row.roomConfigId,
                                row.occupancyTypeId,
                                row.mealPlanId,
                                row.seasonalPeriodId,
                                "mon",
                                value
                              );
                              // Then copy to all days
                              setTimeout(() => {
                                handleCopyToAllDays(
                                  row.roomConfigId,
                                  row.occupancyTypeId,
                                  row.mealPlanId,
                                  row.seasonalPeriodId,
                                  "mon"
                                );
                              }, 0);
                            }}
                            min="0"
                            step="0.01"
                          />
                        </div>
                      </div>
                    </td>
                  )}

                  <td className="px-3 py-4 whitespace-nowrap">
                    <div className="flex justify-center space-x-2">
                      {!row.seasonalPeriodId && (
                        <button
                          className="p-1.5 text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors flex items-center justify-center"
                          onClick={() =>
                            handleCopyBaseToSeasonal(
                              row.roomConfigId,
                              row.occupancyTypeId,
                              row.mealPlanId
                            )
                          }
                          title="Copy base prices to all seasonal periods"
                        >
                          <Calendar className="w-4 h-4" />
                        </button>
                      )}

                      {showAllDays && (
                        <button
                          className="p-1.5 text-blue-600 hover:text-blue-800 bg-blue-50 hover:bg-blue-100 rounded-md transition-colors flex items-center justify-center"
                          onClick={() =>
                            handleCopyToAllDays(
                              row.roomConfigId,
                              row.occupancyTypeId,
                              row.mealPlanId,
                              row.seasonalPeriodId,
                              "mon"
                            )
                          }
                          title="Copy Monday price to all days"
                        >
                          <svg
                            className="w-4 h-4"
                            fill="none"
                            stroke="currentColor"
                            viewBox="0 0 24 24"
                            xmlns="http://www.w3.org/2000/svg"
                          >
                            <path
                              strokeLinecap="round"
                              strokeLinejoin="round"
                              strokeWidth="2"
                              d="M8 7h12m0 0l-4-4m4 4l-4 4m0 6H4m0 0l4 4m-4-4l4-4"
                            ></path>
                          </svg>
                        </button>
                      )}
                    </div>
                  </td>
                </tr>
              );
            })}
          </tbody>
        </table>
      </div>
    </Container>
  );
};

export default ComprehensivePricingTable;
