import {
  <PERSON><PERSON>,
  Input,
  FocusModal,
  Text,
  Label,
  Heading,
  Textarea,
  Tooltip,
  Badge,
} from "@camped-ai/ui";
import CustomSelect from "./custom-select";
import { useForm } from "react-hook-form";
import HotelMediaSection from "./hotel/hotel-media-section";
import { MediaField } from "./hotel/media-item";
import { useState, useEffect } from "react";
import { TextareaField, InputField as AIInput } from "./ai-enhanced-inputs";
import {
  Info,
  Globe,
  MapPin,
  Mail,
  Phone,
  Clock,
  DollarSign,
  Tag,
  Building,
  Image,
  X,
} from "lucide-react";
import { RoomTypeData } from "../types";
import Rating from '@mui/material/Rating';
import VisibilitySettings from "./visibility-settings";

export type HotelFormData = {
  name: string;
  handle: string;
  description: string;
  is_active: boolean;
  is_featured?: boolean;
  website: string | null;
  email: string | null;
  destination_id: string;
  rating?: number;
  total_reviews?: number;
  notes?: string;
  location?: string;
  address?: string;
  phone_number?: string;
  timezone?: string;
  available_languages?: string[];
  tax_type?: string;
  tax_number?: string;
  tags?: string[];
  amenities?: string[];
  rules?: string[];
  safety_measures?: string[];
  currency?: string;
  check_in_time?: string;
  check_out_time?: string;
  is_pets_allowed?: boolean;
  parent_category_id?: string | null;
  media?: MediaField[];
  image_ids?: string[];
  thumbnail_image_id?: string;
  roomTypes?: string[];
  id?: string;
};

type HotelFormProps = {
  formData: HotelFormData;
  roomTypes: RoomTypeData[];
  onSubmit: (data: HotelFormData) => Promise<boolean>;
  closeModal: () => void;
  onSubmitRef?: React.MutableRefObject<(() => Promise<void>) | null>;
};

const HotelFormModern = ({
  formData,
  roomTypes,
  onSubmit,
  closeModal,
  onSubmitRef,
}: HotelFormProps) => {
  const [destinations, setDestinations] = useState<
    Array<{ id: string; name: string; location: string | null }>
  >([]);
  const [activeTab, setActiveTab] = useState("basics");
  const [tags, setTags] = useState<string[]>(() => {
    if (!formData.tags) return [];
    if (Array.isArray(formData.tags)) return formData.tags;
    if (typeof formData.tags === "string") {
      try {
        // Try to parse if it's a JSON string
        const parsed = JSON.parse(formData.tags);
        return Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        // If not a valid JSON, split by comma
        return (formData.tags as string).split(",").map((tag: string) => tag.trim()).filter((tag: string) => tag);
      }
    }
    return [];
  });

  const [tagInput, setTagInput] = useState("");

  // Helper functions for tag management
  const addTag = (tag: string) => {
    const trimmedTag = tag.trim();
    if (trimmedTag && !tags.includes(trimmedTag)) {
      const newTags = [...tags, trimmedTag];
      setTags(newTags);
      form.setValue("tags", newTags);
      setTagInput("");
    }
  };

  const removeTag = (tagToRemove: string) => {
    const newTags = tags.filter(tag => tag !== tagToRemove);
    setTags(newTags);
    form.setValue("tags", newTags);
  };

  const handleTagInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addTag(tagInput);
    } else if (e.key === "Backspace" && tagInput === "" && tags.length > 0) {
      // Remove last tag when backspace is pressed on empty input
      removeTag(tags[tags.length - 1]);
    }
  };

  const handleTagInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    // Handle comma-separated input
    if (value.includes(",")) {
      const newTags = value.split(",").map(tag => tag.trim()).filter(tag => tag);
      newTags.forEach(tag => {
        if (!tags.includes(tag)) {
          addTag(tag);
        }
      });
      setTagInput("");
    } else {
      setTagInput(value);
    }
  };

  // Helper functions for amenities management
  const addAmenity = (amenity: string) => {
    const trimmedAmenity = amenity.trim();
    if (trimmedAmenity && !amenities.includes(trimmedAmenity)) {
      const newAmenities = [...amenities, trimmedAmenity];
      setAmenities(newAmenities);
      form.setValue("amenities", newAmenities);
      setAmenityInput("");
    }
  };

  const removeAmenity = (amenityToRemove: string) => {
    const newAmenities = amenities.filter(amenity => amenity !== amenityToRemove);
    setAmenities(newAmenities);
    form.setValue("amenities", newAmenities);
  };

  const handleAmenityInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addAmenity(amenityInput);
    } else if (e.key === "Backspace" && amenityInput === "" && amenities.length > 0) {
      removeAmenity(amenities[amenities.length - 1]);
    }
  };

  const handleAmenityInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value.includes(",")) {
      const newAmenities = value.split(",").map(item => item.trim()).filter(item => item);
      newAmenities.forEach(amenity => {
        if (!amenities.includes(amenity)) {
          addAmenity(amenity);
        }
      });
      setAmenityInput("");
    } else {
      setAmenityInput(value);
    }
  };

  // Helper functions for rules management
  const addRule = (rule: string) => {
    const trimmedRule = rule.trim();
    if (trimmedRule && !rules.includes(trimmedRule)) {
      const newRules = [...rules, trimmedRule];
      setRules(newRules);
      form.setValue("rules", newRules);
      setRuleInput("");
    }
  };

  const removeRule = (ruleToRemove: string) => {
    const newRules = rules.filter(rule => rule !== ruleToRemove);
    setRules(newRules);
    form.setValue("rules", newRules);
  };

  const handleRuleInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addRule(ruleInput);
    } else if (e.key === "Backspace" && ruleInput === "" && rules.length > 0) {
      removeRule(rules[rules.length - 1]);
    }
  };

  const handleRuleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value.includes(",")) {
      const newRules = value.split(",").map(item => item.trim()).filter(item => item);
      newRules.forEach(rule => {
        if (!rules.includes(rule)) {
          addRule(rule);
        }
      });
      setRuleInput("");
    } else {
      setRuleInput(value);
    }
  };

  // Helper functions for safety measures management
  const addSafetyMeasure = (measure: string) => {
    const trimmedMeasure = measure.trim();
    if (trimmedMeasure && !safetyMeasures.includes(trimmedMeasure)) {
      const newSafetyMeasures = [...safetyMeasures, trimmedMeasure];
      setSafetyMeasures(newSafetyMeasures);
      form.setValue("safety_measures", newSafetyMeasures);
      setSafetyMeasureInput("");
    }
  };

  const removeSafetyMeasure = (measureToRemove: string) => {
    const newSafetyMeasures = safetyMeasures.filter(measure => measure !== measureToRemove);
    setSafetyMeasures(newSafetyMeasures);
    form.setValue("safety_measures", newSafetyMeasures);
  };

  const handleSafetyMeasureInputKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter") {
      e.preventDefault();
      addSafetyMeasure(safetyMeasureInput);
    } else if (e.key === "Backspace" && safetyMeasureInput === "" && safetyMeasures.length > 0) {
      removeSafetyMeasure(safetyMeasures[safetyMeasures.length - 1]);
    }
  };

  const handleSafetyMeasureInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    if (value.includes(",")) {
      const newSafetyMeasures = value.split(",").map(item => item.trim()).filter(item => item);
      newSafetyMeasures.forEach(measure => {
        if (!safetyMeasures.includes(measure)) {
          addSafetyMeasure(measure);
        }
      });
      setSafetyMeasureInput("");
    } else {
      setSafetyMeasureInput(value);
    }
  };

  const [amenities, setAmenities] = useState<string[]>(() => {
    if (!formData.amenities) return [];
    if (Array.isArray(formData.amenities)) return formData.amenities;
    if (typeof formData.amenities === "string") {
      try {
        const parsed = JSON.parse(formData.amenities);
        return Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        return (formData.amenities as string).split(",").map((item: string) => item.trim()).filter((item: string) => item);
      }
    }
    return [];
  });

  const [rules, setRules] = useState<string[]>(() => {
    if (!formData.rules) return [];
    if (Array.isArray(formData.rules)) return formData.rules;
    if (typeof formData.rules === "string") {
      try {
        const parsed = JSON.parse(formData.rules);
        return Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        return (formData.rules as string).split(",").map((item: string) => item.trim()).filter((item: string) => item);
      }
    }
    return [];
  });

  const [safetyMeasures, setSafetyMeasures] = useState<string[]>(() => {
    if (!formData.safety_measures) return [];
    if (Array.isArray(formData.safety_measures)) return formData.safety_measures;
    if (typeof formData.safety_measures === "string") {
      try {
        const parsed = JSON.parse(formData.safety_measures);
        return Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        return (formData.safety_measures as string).split(",").map((item: string) => item.trim()).filter((item: string) => item);
      }
    }
    return [];
  });

  const [amenityInput, setAmenityInput] = useState("");
  const [ruleInput, setRuleInput] = useState("");
  const [safetyMeasureInput, setSafetyMeasureInput] = useState("");

  // Destination visibility settings state (UI only)
  const [destinationVisibility, setDestinationVisibility] = useState({
    show_in_destination_listings: false,
    show_in_destination_highlights: false,
    show_in_destination_map: false,
    allow_destination_booking: false,
  });

  const form = useForm<HotelFormData>({
    defaultValues: {
      ...formData,
      tags: Array.isArray(formData.tags) ? formData.tags : [],
      amenities: Array.isArray(formData.amenities) ? formData.amenities : [],
      rules: Array.isArray(formData.rules) ? formData.rules : [],
      safety_measures: Array.isArray(formData.safety_measures) ? formData.safety_measures : []
    },
  });

  const isEdit = !!formData.id;

  // Reset form when formData changes (e.g., when creating a new hotel after submitting one)
  useEffect(() => {
    // Reset the form with the new formData
    form.reset(formData);

    // Reset all array states and sync with form
    let newTags: string[] = [];
    if (!formData.tags) {
      newTags = [];
    } else if (Array.isArray(formData.tags)) {
      newTags = formData.tags;
    } else if (typeof formData.tags === "string") {
      try {
        const parsed = JSON.parse(formData.tags);
        newTags = Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        newTags = (formData.tags as string).split(",").map((tag: string) => tag.trim()).filter((tag: string) => tag);
      }
    }
    setTags(newTags);
    form.setValue("tags", newTags);
    setTagInput("");

    // Reset amenities
    let newAmenities: string[] = [];
    if (!formData.amenities) {
      newAmenities = [];
    } else if (Array.isArray(formData.amenities)) {
      newAmenities = formData.amenities;
    } else if (typeof formData.amenities === "string") {
      try {
        const parsed = JSON.parse(formData.amenities);
        newAmenities = Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        newAmenities = (formData.amenities as string).split(",").map((item: string) => item.trim()).filter((item: string) => item);
      }
    }
    setAmenities(newAmenities);
    form.setValue("amenities", newAmenities);
    setAmenityInput("");

    // Reset rules
    let newRules: string[] = [];
    if (!formData.rules) {
      newRules = [];
    } else if (Array.isArray(formData.rules)) {
      newRules = formData.rules;
    } else if (typeof formData.rules === "string") {
      try {
        const parsed = JSON.parse(formData.rules);
        newRules = Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        newRules = (formData.rules as string).split(",").map((item: string) => item.trim()).filter((item: string) => item);
      }
    }
    setRules(newRules);
    form.setValue("rules", newRules);
    setRuleInput("");

    // Reset safety measures
    let newSafetyMeasures: string[] = [];
    if (!formData.safety_measures) {
      newSafetyMeasures = [];
    } else if (Array.isArray(formData.safety_measures)) {
      newSafetyMeasures = formData.safety_measures;
    } else if (typeof formData.safety_measures === "string") {
      try {
        const parsed = JSON.parse(formData.safety_measures);
        newSafetyMeasures = Array.isArray(parsed) ? parsed : [];
      } catch (e) {
        newSafetyMeasures = (formData.safety_measures as string).split(",").map((item: string) => item.trim()).filter((item: string) => item);
      }
    }
    setSafetyMeasures(newSafetyMeasures);
    form.setValue("safety_measures", newSafetyMeasures);
    setSafetyMeasureInput("");
  }, [formData, form]);

  // Ensure initial arrays are synced with form on component mount
  useEffect(() => {
    form.setValue("tags", tags);
    form.setValue("amenities", amenities);
    form.setValue("rules", rules);
    form.setValue("safety_measures", safetyMeasures);
  }, []);

  // Fetch destinations
  useEffect(() => {
    fetch("/admin/hotel-management/destinations", { credentials: "include" })
      .then((res) => res.json())
      .then((data) => setDestinations(data.destinations))
      .catch((error) => console.error("Error fetching destinations:", error));
  }, []);

  // Handle form submission
  const handleSubmit = async () => {
    // Get the form values including media
    const formValues = form.getValues();

    // Basic validation for required fields
    if (!formValues.name || !formValues.handle || !formValues.destination_id) {
      console.error("Missing required fields:", {
        name: !formValues.name,
        handle: !formValues.handle,
        destination_id: !formValues.destination_id,
      });
      return;
    }

    // Use our local state arrays directly (they are the source of truth)
    const finalTags = tags.length > 0 ? tags : undefined;
    const finalAmenities = amenities.length > 0 ? amenities : undefined;
    const finalRules = rules.length > 0 ? rules : undefined;
    const finalSafetyMeasures = safetyMeasures.length > 0 ? safetyMeasures : undefined;

    // Ensure check-in and check-out times are properly formatted
    const checkInTime = formValues.check_in_time || "14:00";
    const checkOutTime = formValues.check_out_time || "11:00";

    // Prepare the data for submission
    const submissionData = {
      ...formValues,
      tags: finalTags,
      amenities: finalAmenities,
      rules: finalRules,
      safety_measures: finalSafetyMeasures,
      check_in_time: checkInTime,
      check_out_time: checkOutTime,
    };

    // Debug: Log the submission data to verify all arrays are included
    console.log("Hotel form submission data:", {
      tags: finalTags,
      amenities: finalAmenities,
      rules: finalRules,
      safetyMeasures: finalSafetyMeasures,
      formValues: {
        tags: formValues.tags,
        amenities: formValues.amenities,
        rules: formValues.rules,
        safety_measures: formValues.safety_measures
      },
      localStates: {
        tags,
        amenities,
        rules,
        safetyMeasures
      },
      submissionData
    });

    const success = await onSubmit(submissionData);
    if (success) {
      closeModal();
    }
  };

  // Expose handleSubmit function to parent component
  useEffect(() => {
    if (onSubmitRef) {
      onSubmitRef.current = handleSubmit;
    }
  }, [onSubmitRef, handleSubmit]);

  return (
    <>
      <div className="w-full">
        <div className="bg-white border-b border-gray-200 mb-6">
          <nav className="flex space-x-8">
            <button
              onClick={() => setActiveTab("basics")}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${activeTab === "basics"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
            >
              <Building size={18} />
              <span>Basic Information</span>
            </button>
            <button
              onClick={() => setActiveTab("contact")}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${activeTab === "contact"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
            >
              <Mail size={18} />
              <span>Contact Details</span>
            </button>
            <button
              onClick={() => setActiveTab("features")}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${activeTab === "features"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
            >
              <Tag size={18} />
              <span>Features & Amenities</span>
            </button>
            <button
              onClick={() => setActiveTab("media")}
              className={`py-4 px-1 border-b-2 font-medium text-sm flex items-center gap-2 ${activeTab === "media"
                ? "border-blue-500 text-blue-600"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
            >
              <Image size={18} />
              <span>Media</span>
            </button>
          </nav>
        </div>
        <div className="">
          {activeTab === "basics" && (
            <div className="space-y-6">
              <div className="bg-white pt-4 p-6 rounded-lg shadow-sm border border-gray-200">
                <h3 className="text-lg font-medium mb-4">Hotel Information</h3>
                <div className="space-y-4">
                  <div>
                    <AIInput
                      id="name"
                      label="Hotel Name"
                      required
                      value={form.watch("name") || ""}
                      onChange={(value) => {
                        form.setValue("name", value);
                        form.setValue(
                          "handle",
                          value.toLowerCase().replace(/\s+/g, "-")
                        );
                      }}
                      placeholder="e.g. Grand Hotel"
                      contentType="name"
                      context={{
                        type: "hotel",
                        destination: destinations.find(
                          (d) => d.id === form.watch("destination_id")
                        )?.name,
                      }}
                    />
                    <Text className="text-xs text-gray-500 mt-1">
                      The name of the hotel as it will appear to users
                    </Text>
                  </div>

                  <div>
                    <Label htmlFor="handle" className="block mb-1 font-medium">
                      Handle <span className="text-red-500">*</span>
                    </Label>
                    <Input
                      id="handle"
                      {...form.register("handle", { required: true })}
                      placeholder="e.g. grand-hotel"
                      className="w-full"
                    />
                    <Text className="text-xs text-gray-500 mt-1">
                      Used for the URL and internal references
                    </Text>
                  </div>

                  <div>
                    <TextareaField
                      id="description"
                      label="Description"
                      value={form.watch("description") || ""}
                      onChange={(value) => form.setValue("description", value)}
                      placeholder="Enter a detailed description of the hotel"
                      rows={5}
                      contentType="description"
                      context={{
                        name: form.watch("name"),
                        type: "hotel",
                        destination: destinations.find(
                          (d) => d.id === form.watch("destination_id")
                        )?.name,
                      }}
                    />
                  </div>

                  <div>
                    <Label
                      htmlFor="destination"
                      className="block mb-1 font-medium"
                    >
                      Destination <span className="text-red-500">*</span>
                    </Label>
                    <CustomSelect
                      id="destination"
                      options={destinations.map((destination) => ({
                        value: destination.id,
                        label: `${destination.name} ${destination.location
                          ? `(${destination.location})`
                          : ""
                          }`,
                      }))}
                      value={form.watch("destination_id") || ""}
                      onChange={(value) => {
                        console.log("Destination changed to:", value);
                        form.setValue("destination_id", value);
                      }}
                      placeholder="Select a destination"
                      className="w-full"
                    />
                  </div>

                  <div>
                    <Label
                      htmlFor="location"
                      className="block mb-1 font-medium"
                    >
                      Location
                    </Label>
                    <div className="relative">
                      <MapPin
                        size={16}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                      />
                      <Input
                        id="location"
                        {...form.register("location")}
                        placeholder="e.g. 123 Main Street"
                        className="w-full "
                      />
                    </div>
                  </div>

                  <div>
                    <Label
                      htmlFor="rating"
                      className="block mb-1 font-medium"
                    >
                      Rating
                    </Label>
                    <div className="mt-2">
                      <Input
                        id="rating"
                        type="number"
                        step="0.1"
                        min="0"
                        max="5"
                        {...form.register("rating", {
                          valueAsNumber: true,
                          min: 0,
                          max: 5,
                        })}
                        placeholder="e.g. 4.5"
                        className="w-full"
                      />
                      <Rating name="size-small" value={Number(form.watch("rating")) || 0} readOnly precision={0.5} className="mt-3" />
                    </div>
                  </div>
                </div>
              </div>

              <VisibilitySettings
                title="Visibility Settings"
                options={[
                  {
                    id: "is_active",
                    label: "Active",
                    description: "When active, the hotel will be visible to users",
                    checked: form.watch("is_active"),
                    onChange: (checked) => form.setValue("is_active", checked)
                  },
                  {
                    id: "is_featured",
                    label: "Featured",
                    description: "Featured hotels will be highlighted and shown prominently to users",
                    checked: form.watch("is_featured") || false,
                    onChange: (checked) => form.setValue("is_featured", checked)
                  },
                  {
                    id: "is_pets_allowed",
                    label: "Pets Allowed",
                    description: "When enabled, this hotel allows pets",
                    checked: form.watch("is_pets_allowed") || false,
                    onChange: (checked) => form.setValue("is_pets_allowed", checked)
                  }
                ]}
              />

              <VisibilitySettings
                title="Destination Visibility Settings"
                options={[
                  {
                    id: "show_in_destination_listings",
                    label: "Show in Destination Listings",
                    description: "When enabled, this hotel will appear in destination search results and listings",
                    checked: destinationVisibility.show_in_destination_listings,
                    onChange: (checked) => setDestinationVisibility(prev => ({ ...prev, show_in_destination_listings: checked }))
                  },
                  {
                    id: "show_in_destination_highlights",
                    label: "Show in Destination Highlights",
                    description: "Featured prominently on the destination page as a highlighted accommodation",
                    checked: destinationVisibility.show_in_destination_highlights,
                    onChange: (checked) => setDestinationVisibility(prev => ({ ...prev, show_in_destination_highlights: checked }))
                  },
                  {
                    id: "show_in_destination_map",
                    label: "Show on Destination Map",
                    description: "Display this hotel as a pin on the destination's interactive map",
                    checked: destinationVisibility.show_in_destination_map,
                    onChange: (checked) => setDestinationVisibility(prev => ({ ...prev, show_in_destination_map: checked }))
                  },
                  {
                    id: "allow_destination_booking",
                    label: "Allow Destination Booking",
                    description: "Enable direct booking from destination pages and search results",
                    checked: destinationVisibility.allow_destination_booking,
                    onChange: (checked) => setDestinationVisibility(prev => ({ ...prev, allow_destination_booking: checked }))
                  }
                ]}
              />
            </div>
          )}
          {/* Contact Details Tab */}
          {activeTab === "contact" && (
            <div className="space-y-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <h3 className="text-lg font-medium mb-4">
                  Contact Information
                </h3>
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="website" className="block mb-1 font-medium">
                      Website
                    </Label>
                    <div className="relative">
                      <Globe
                        size={16}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                      />
                      <Input
                        id="website"
                        {...form.register("website")}
                        placeholder="e.g. https://www.grandhotel.com"
                        className="w-full"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="email" className="block mb-1 font-medium">
                      Email Address
                    </Label>
                    <div className="relative">
                      <Mail
                        size={16}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                      />
                      <Input
                        id="email"
                        type="email"
                        {...form.register("email")}
                        placeholder="e.g. <EMAIL>"
                        className="w-full"
                      />
                    </div>
                  </div>

                  <div>
                    <Label
                      htmlFor="phone_number"
                      className="block mb-1 font-medium"
                    >
                      Phone Number
                    </Label>
                    <div className="relative">
                      <Phone
                        size={16}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                      />
                      <Input
                        id="phone_number"
                        {...form.register("phone_number")}
                        placeholder="e.g. +****************"
                        className="w-full"
                      />
                    </div>
                  </div>

                  <div>
                    <Label htmlFor="address" className="block mb-1 font-medium">
                      Full Address
                    </Label>
                    <Textarea
                      id="address"
                      {...form.register("address")}
                      placeholder="Enter the complete address"
                      className="w-full"
                    />
                  </div>
                </div>
              </div>

              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <h3 className="text-lg font-medium mb-4">
                  Additional Information
                </h3>
                <div className="space-y-4">
                  <div>
                    <Label
                      htmlFor="check_in_time"
                      className="mb-1 font-medium flex items-center gap-1"
                    >
                      Check-in Time
                      <Tooltip content="The standard check-in time for the hotel">
                        <Info size={14} className="text-gray-400" />
                      </Tooltip>
                    </Label>
                    <div className="relative">
                      <Clock
                        size={16}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                      />
                      <Input
                        id="check_in_time"
                        type="time"
                        {...form.register("check_in_time")}
                        placeholder="14:00"
                        className="w-full"
                        defaultValue={form.watch("check_in_time") || "14:00"}
                      />
                    </div>
                    <Text className="text-xs text-gray-500 mt-1">
                      Standard check-in time in 24-hour format (e.g., 14:00 for
                      2:00 PM)
                    </Text>
                  </div>

                  <div>
                    <Label
                      htmlFor="check_out_time"
                      className="mb-1 font-medium flex items-center gap-1"
                    >
                      Check-out Time
                      <Tooltip content="The standard check-out time for the hotel">
                        <Info size={14} className="text-gray-400" />
                      </Tooltip>
                    </Label>
                    <div className="relative">
                      <Clock
                        size={16}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                      />
                      <Input
                        id="check_out_time"
                        type="time"
                        {...form.register("check_out_time")}
                        placeholder="11:00"
                        className="w-full "
                        defaultValue={form.watch("check_out_time") || "11:00"}
                      />
                    </div>
                    <Text className="text-xs text-gray-500 mt-1">
                      Standard check-out time in 24-hour format (e.g., 11:00 for
                      11:00 AM)
                    </Text>
                  </div>

                  <div>
                    <Label
                      htmlFor="currency"
                      className="block mb-1 font-medium"
                    >
                      Currency
                    </Label>
                    <div className="relative">
                      <DollarSign
                        size={16}
                        className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400"
                      />
                      <Input
                        id="currency"
                        {...form.register("currency")}
                        placeholder="e.g. USD"
                        className="w-full "
                      />
                    </div>
                  </div>
                </div>
              </div>
            </div>
          )}
          {/* Features & Amenities Tab */}
          {activeTab === "features" && (
            <div className="space-y-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <div className="space-y-4">
                  <div>
                    <Label htmlFor="tags" className="block mb-1 font-medium">
                      Tags
                    </Label>

                    {/* Tags Display */}
                    {tags.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-2 p-2 border border-gray-200 rounded-md bg-gray-50">
                        {tags.map((tag, index) => (
                          <Badge
                            key={index}
                            className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-blue-100 text-blue-800 rounded-full"
                          >
                            {tag}
                            <button
                              type="button"
                              onClick={() => removeTag(tag)}
                              className="ml-1 hover:bg-blue-200 rounded-full p-0.5 transition-colors"
                              aria-label={`Remove ${tag} tag`}
                            >
                              <X size={12} />
                            </button>
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* Input Field */}
                    <Input
                      id="tags"
                      value={tagInput}
                      onChange={handleTagInputChange}
                      onKeyDown={handleTagInputKeyDown}
                      placeholder="Type a tag and press Enter"
                      className="w-full"
                    />
                  </div>

                  <div>
                    <Label
                      htmlFor="amenities"
                      className="mb-1 font-medium flex items-center gap-1"
                    >
                      Amenities
                      <Tooltip content="List of amenities available at the hotel">
                        <Info size={14} className="text-gray-400" />
                      </Tooltip>
                    </Label>

                    {/* Amenities Display */}
                    {amenities.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-2 p-2 border border-gray-200 rounded-md bg-gray-50">
                        {amenities.map((amenity, index) => (
                          <Badge
                            key={index}
                            className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-green-100 text-green-800 rounded-full"
                          >
                            {amenity}
                            <button
                              type="button"
                              onClick={() => removeAmenity(amenity)}
                              className="ml-1 hover:bg-green-200 rounded-full p-0.5 transition-colors"
                              aria-label={`Remove ${amenity} amenity`}
                            >
                              <X size={12} />
                            </button>
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* Input Field */}
                    <Input
                      id="amenities"
                      value={amenityInput}
                      onChange={handleAmenityInputChange}
                      onKeyDown={handleAmenityInputKeyDown}
                      placeholder="Type an amenity and press Enter"
                      className="w-full"
                    />
                  </div>

                  <div>
                    <Label
                      htmlFor="rules"
                      className="mb-1 font-medium flex items-center gap-1"
                    >
                      Rules
                      <Tooltip content="List of rules and policies for the hotel">
                        <Info size={14} className="text-gray-400" />
                      </Tooltip>
                    </Label>

                    {/* Rules Display */}
                    {rules.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-2 p-2 border border-gray-200 rounded-md bg-gray-50">
                        {rules.map((rule, index) => (
                          <Badge
                            key={index}
                            className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-orange-100 text-orange-800 rounded-full"
                          >
                            {rule}
                            <button
                              type="button"
                              onClick={() => removeRule(rule)}
                              className="ml-1 hover:bg-orange-200 rounded-full p-0.5 transition-colors"
                              aria-label={`Remove ${rule} rule`}
                            >
                              <X size={12} />
                            </button>
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* Input Field */}
                    <Input
                      id="rules"
                      value={ruleInput}
                      onChange={handleRuleInputChange}
                      onKeyDown={handleRuleInputKeyDown}
                      placeholder="Type a rule and press Enter"
                      className="w-full"
                    />
                  </div>

                  <div>
                    <Label
                      htmlFor="safety_measures"
                      className="mb-1 font-medium flex items-center gap-1"
                    >
                      Safety Measures
                      <Tooltip content="List of safety measures implemented at the hotel">
                        <Info size={14} className="text-gray-400" />
                      </Tooltip>
                    </Label>

                    {/* Safety Measures Display */}
                    {safetyMeasures.length > 0 && (
                      <div className="flex flex-wrap gap-2 mb-2 p-2 border border-gray-200 rounded-md bg-gray-50">
                        {safetyMeasures.map((measure, index) => (
                          <Badge
                            key={index}
                            className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium bg-red-100 text-red-800 rounded-full"
                          >
                            {measure}
                            <button
                              type="button"
                              onClick={() => removeSafetyMeasure(measure)}
                              className="ml-1 hover:bg-red-200 rounded-full p-0.5 transition-colors"
                              aria-label={`Remove ${measure} safety measure`}
                            >
                              <X size={12} />
                            </button>
                          </Badge>
                        ))}
                      </div>
                    )}

                    {/* Input Field */}
                    <Input
                      id="safety_measures"
                      value={safetyMeasureInput}
                      onChange={handleSafetyMeasureInputChange}
                      onKeyDown={handleSafetyMeasureInputKeyDown}
                      placeholder="Type a safety measure and press Enter"
                      className="w-full"
                    />
                  </div>

                  <div>
                    <TextareaField
                      id="notes"
                      label="Additional Notes"
                      value={form.watch("notes") || ""}
                      onChange={(value) => form.setValue("notes", value)}
                      placeholder="Any additional information about the hotel"
                      rows={5}
                      contentType="description"
                      context={{
                        name: form.watch("name"),
                        type: "hotel notes",
                        description: form.watch("description"),
                      }}
                      helpText="Add any special instructions, policies, or other information about the hotel"
                    />
                  </div>
                </div>
              </div>
            </div>
          )}
          {/* Media Tab */}
          {activeTab === "media" && (
            <div className="space-y-6">
              <div className="bg-white p-6 rounded-lg shadow-sm border border-gray-200">
                <h3 className="text-lg font-medium mb-4">Hotel Images</h3>
                <Text className="text-sm text-gray-500 mb-4">
                  Upload images that showcase this hotel. The first image or the
                  one marked as thumbnail will be used as the main image.
                </Text>

                <HotelMediaSection form={form} />
              </div>
            </div>
          )}
        </div>
      </div>
    </>
  );
};

export default HotelFormModern;
